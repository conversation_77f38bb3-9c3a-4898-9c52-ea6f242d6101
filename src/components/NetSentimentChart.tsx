import React from "react";

interface SurveyResponse {
  "Strongly Disagree": number;
  Disagree: number;
  Neutral: number;
  Agree: number;
  "Strongly Agree": number;
}

interface SurveyResult {
  statement: string;
  responses: SurveyResponse;
  net_sentiment: number;
}

interface NetSentimentChartProps {
  data: SurveyResult[];
}

export const NetSentimentChart: React.FC<NetSentimentChartProps> = ({
  data,
}) => {
  const getSentimentColor = (sentiment: number) => {
    if (sentiment >= 50) return "text-green-600"; // High positive
    if (sentiment >= 20) return "text-green-500"; // Moderate positive
    if (sentiment >= 0) return "text-green-400"; // Low positive
    return "text-red-500"; // Negative
  };

  const formatSentiment = (sentiment: number) => {
    return sentiment > 0
      ? `+${sentiment.toFixed(0)}%`
      : `${sentiment.toFixed(0)}%`;
  };

  return (
    <div className="bg-gray-100 p-8 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            SURVEY RESULTS
          </h1>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {data.slice(0, 4).map((item, index) => (
            <div key={index} className="bg-white rounded-lg shadow-lg p-6">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  {item.statement.toUpperCase()}
                </h3>

                <div
                  className={`text-5xl font-bold mb-2 ${getSentimentColor(item.net_sentiment)}`}
                >
                  {formatSentiment(item.net_sentiment)}
                </div>

                <div className="text-sm text-gray-600 font-medium">
                  NET
                  <br />
                  SENTIMENT
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional statements in a row below if there are more than 4 */}
        {data.length > 4 && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            {data.slice(4).map((item, index) => (
              <div
                key={index + 4}
                className="bg-white rounded-lg shadow-lg p-6"
              >
                <div className="text-center">
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">
                    {item.statement.toUpperCase()}
                  </h3>

                  <div
                    className={`text-5xl font-bold mb-2 ${getSentimentColor(item.net_sentiment)}`}
                  >
                    {formatSentiment(item.net_sentiment)}
                  </div>

                  <div className="text-sm text-gray-600 font-medium">
                    NET SENTIMENT
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
