import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  ChevronLeft,
  Download,
  Share2,
  <PERSON><PERSON>hart2,
  ArrowUpRight,
  MessageSquare,
  ThumbsUp,
  Users,
} from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import HeaderClient from "./HeaderClient";
import { SurveyResultsChart } from "@/components/SurveyResultsChart";
import { NetSentimentChart } from "@/components/NetSentimentChart";

// Dummy data structured to simulate backend response
const conceptTestData = {
  id: "ct-123456",
  title: "Product Concept Test",
  createdAt: "2023-12-15T10:30:00Z",
  status: "completed", // completed, in-progress, error
  image: "/placeholder-image.jpg", // This would be the S3 URL in real data
  description:
    "A sleek, eco-friendly water bottle with temperature control and hydration tracking. The bottle connects to your smartphone via Bluetooth and reminds you when it's time to hydrate. It's made from 100% recycled materials and keeps drinks cold for 24 hours or hot for 12 hours.",
  when: "2024",
  where: {
    name: "United States of America (USA)",
    flag: "http://purecatamphetamine.github.io/country-flag-icons/3x2/US.svg",
  },
  audience: {
    size: 150,
    demographics: [
      { name: "Age", value: "25-45" },
      { name: "Gender", value: "All genders" },
      { name: "Income", value: "$40,000-$100,000" },
      { name: "Education", value: "College degree or higher" },
    ],
  },
  questions: [
    {
      id: "q1",
      text: "How likely are you to purchase this product?",
      label: "Purchase Intent",
      results: {
        average: 7.8,
        distribution: [
          { value: 1, count: 2 },
          { value: 2, count: 3 },
          { value: 3, count: 5 },
          { value: 4, count: 7 },
          { value: 5, count: 12 },
          { value: 6, count: 15 },
          { value: 7, count: 25 },
          { value: 8, count: 40 },
          { value: 9, count: 30 },
          { value: 10, count: 11 },
        ],
      },
    },
    {
      id: "q2",
      text: "How unique is this concept?",
      label: "Uniqueness",
      results: {
        average: 8.5,
        distribution: [
          { value: 1, count: 1 },
          { value: 2, count: 2 },
          { value: 3, count: 3 },
          { value: 4, count: 5 },
          { value: 5, count: 7 },
          { value: 6, count: 10 },
          { value: 7, count: 15 },
          { value: 8, count: 35 },
          { value: 9, count: 45 },
          { value: 10, count: 27 },
        ],
      },
    },
    {
      id: "q3",
      text: "How well does this concept fit your needs?",
      label: "Need Fit",
      results: {
        average: 6.9,
        distribution: [
          { value: 1, count: 5 },
          { value: 2, count: 8 },
          { value: 3, count: 10 },
          { value: 4, count: 12 },
          { value: 5, count: 15 },
          { value: 6, count: 18 },
          { value: 7, count: 32 },
          { value: 8, count: 28 },
          { value: 9, count: 15 },
          { value: 10, count: 7 },
        ],
      },
    },
  ],
  feedback: [
    {
      id: "f1",
      text: "I really like the temperature control feature, that would be useful for my daily commute.",
      sentiment: "positive",
    },
    {
      id: "f2",
      text: "The price point seems high for what it offers. I'm not sure if I'd pay that much.",
      sentiment: "negative",
    },
    {
      id: "f3",
      text: "I like the hydration tracking, but I'm concerned about privacy with the Bluetooth connectivity.",
      sentiment: "neutral",
    },
  ],
};

const surveyData = {
  survey_results: [
    {
      statement: "Statement 1",
      responses: {
        "Strongly Disagree": 10,
        Disagree: 15,
        Neutral: 20,
        Agree: 35,
        "Strongly Agree": 20,
      },
      net_sentiment: 30.0,
    },
    {
      statement: "Statement 2",
      responses: {
        "Strongly Disagree": 5,
        Disagree: 10,
        Neutral: 15,
        Agree: 45,
        "Strongly Agree": 25,
      },
      net_sentiment: 55.0,
    },
    {
      statement: "Statement 3",
      responses: {
        "Strongly Disagree": 12,
        Disagree: 18,
        Neutral: 30,
        Agree: 25,
        "Strongly Agree": 15,
      },
      net_sentiment: 10.0,
    },
    {
      statement: "Statement 4",
      responses: {
        "Strongly Disagree": 2,
        Disagree: 5,
        Neutral: 10,
        Agree: 50,
        "Strongly Agree": 33,
      },
      net_sentiment: 76.0,
    },
    {
      statement: "Statement 5",
      responses: {
        "Strongly Disagree": 8,
        Disagree: 12,
        Neutral: 25,
        Agree: 30,
        "Strongly Agree": 25,
      },
      net_sentiment: 35.0,
    },
  ],
};

// Server Component
async function ConceptTestPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;

  // Using the dummy data for now
  const data = conceptTestData;

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  return (
    <div className="flex flex-col min-h-screen bg-background">
      {/* Top navigation bar */}
      <div className="sticky top-0 z-10 bg-background border-b border-border">
        <div className="container mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <Link
              href="/concept-testing/results"
              className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
            >
              <ChevronLeft className="h-5 w-5 mr-1" />
              <span>Back to Tests</span>
            </Link>
          </div>
          <div className="flex h-11 py-3 px-2 justify-center items-center gap-2 border border-solid rounded-lg border-card-border bg-white hover:bg-secondary-grey shadow-sm">
            <HeaderClient />
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 container mx-auto px-4 sm:px-6 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-semibold text-text-dark">
              {data.title}
            </h1>
            <div className="flex items-center gap-2 mt-2 text-muted-foreground">
              <span>{formatDate(data.createdAt)}</span>
              <span className="inline-block h-1 w-1 rounded-full bg-muted-foreground"></span>
              <span className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                {data.audience.size} respondents
              </span>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button variant="outline" className="h-10 gap-2">
              <Download className="h-4 w-4" />
              Export
            </Button>
            <Button variant="outline" className="h-10 gap-2">
              <Share2 className="h-4 w-4" />
              Share
            </Button>
          </div>
        </div>

        {/* Summary cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
          {data.questions.map((question) => (
            <div
              key={question.id}
              className="bg-white rounded-lg border border-input shadow-sm p-5"
            >
              <div className="flex justify-between items-start mb-3">
                <h3 className="font-medium text-sm text-muted-foreground">
                  {question.label}
                </h3>
                <div className="flex items-center justify-center bg-primary/10 text-primary px-2 py-1 rounded-full">
                  <span className="text-sm font-medium">
                    {question.results.average}/10
                  </span>
                </div>
              </div>
              <p className="text-text-default text-sm mb-4">{question.text}</p>

              {/* Simple bar chart visualization */}
              <div className="h-20 flex items-end justify-between gap-1">
                {question.results.distribution.map((item, index) => {
                  const heightPercentage =
                    (item.count /
                      Math.max(
                        ...question.results.distribution.map((d) => d.count)
                      )) *
                    100;
                  return (
                    <div
                      key={index}
                      className="flex-1 flex flex-col items-center"
                    >
                      <div
                        className="w-full bg-primary-light hover:bg-primary/60 transition-colors rounded-t"
                        style={{ height: `${heightPercentage}%` }}
                      ></div>
                      {(index + 1) % 2 === 0 && (
                        <span className="text-xs text-muted-foreground mt-1">
                          {index + 1}
                        </span>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {/* Survey Results Charts */}
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8">
          <div className="p-6">
            <h2 className="text-xl font-semibold text-text-default mb-6">
              Survey Analysis
            </h2>
            <div className="space-y-8">
              <SurveyResultsChart data={surveyData.survey_results} />
              <NetSentimentChart data={surveyData.survey_results} />
            </div>
          </div>
        </div>

        {/* Concept Details */}
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8">
          <div className="p-6 border-b border-input">
            <h2 className="text-xl font-semibold text-text-default mb-1">
              Concept Details
            </h2>
            <p className="text-sm text-muted-foreground mb-6">
              Information about the tested concept
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Image */}
              <div>
                <h3 className="text-base font-medium mb-3">Concept Image</h3>
                <div className="relative aspect-square w-full overflow-hidden rounded-lg border border-input">
                  <Image
                    src={data.image}
                    alt="Concept"
                    fill
                    className="object-cover"
                  />
                </div>
              </div>

              {/* Description and metadata */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-base font-medium mb-3">Description</h3>
                  <div className="p-4 bg-muted/20 border border-input rounded-lg">
                    <p className="text-text-default">{data.description}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium mb-2">When</h3>
                    <div className="p-3 bg-muted/20 rounded-md border border-input text-sm">
                      {data.when}
                    </div>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium mb-2">Where</h3>
                    <div className="p-3 bg-muted/20 rounded-md border border-input text-sm flex items-center gap-2">
                      {data.where.flag && (
                        <Image
                          src={data.where.flag}
                          alt={`${data.where.name} flag`}
                          width={24}
                          height={16}
                          className="inline-block mr-1"
                        />
                      )}
                      {data.where.name}
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-base font-medium mb-3">
                    Target Audience
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    {data.audience.demographics.map((demo, idx) => (
                      <div
                        key={idx}
                        className="flex flex-col p-3 bg-muted/20 rounded-md border border-input"
                      >
                        <span className="text-xs text-muted-foreground mb-1">
                          {demo.name}
                        </span>
                        <span className="text-sm font-medium">
                          {demo.value}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Feedback section */}
        <div className="bg-white rounded-lg border border-input shadow-sm mb-8">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <div>
                <h2 className="text-xl font-semibold text-text-default mb-1">
                  Qualitative Feedback
                </h2>
                <p className="text-sm text-muted-foreground">
                  Responses from participants
                </p>
              </div>
              <Button variant="outline" className="h-9 gap-2 text-sm">
                <BarChart2 className="h-4 w-4" />
                See All Data
              </Button>
            </div>

            <div className="space-y-4">
              {data.feedback.map((item) => (
                <div
                  key={item.id}
                  className="p-4 bg-muted/10 rounded-lg border border-input"
                >
                  <div className="flex items-start gap-2">
                    <MessageSquare className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                    <div>
                      <p className="text-text-default">{item.text}</p>
                      <div className="mt-2 flex items-center gap-2">
                        {item.sentiment === "positive" && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <ThumbsUp className="h-3 w-3 mr-1" /> Positive
                          </span>
                        )}
                        {item.sentiment === "negative" && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            <ThumbsUp className="h-3 w-3 mr-1 rotate-180" />{" "}
                            Negative
                          </span>
                        )}
                        {item.sentiment === "neutral" && (
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            Neutral
                          </span>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Call to action */}
        <div className="bg-primary/5 rounded-lg border border-primary/20 p-6 flex flex-col md:flex-row justify-between items-center gap-4">
          <div>
            <h3 className="text-lg font-semibold text-text-dark mb-1">
              Want to test another concept?
            </h3>
            <p className="text-muted-foreground">
              Create a new concept test and gather insights from your target
              audience.
            </p>
          </div>
          <Button className="whitespace-nowrap gap-2">
            Create New Test
            <ArrowUpRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

export default ConceptTestPage;
